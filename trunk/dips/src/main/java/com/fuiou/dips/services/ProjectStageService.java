package com.fuiou.dips.services;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.fuiou.dips.convert.ProjectConvertMapper;
import com.fuiou.dips.data.entity.CompensationStep;
import com.fuiou.dips.data.req.ProjectStageReq;
import com.fuiou.dips.enums.LockFlagEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.context.ApplicationContextKeeper;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.dipsdb.ProjectStageMapper;
import com.fuiou.dips.utils.CollectionUtils;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 项目阶段服务
 *
 * <AUTHOR>
 */
@Service
public class ProjectStageService {

    private static final Logger log = LoggerFactory.getLogger(ProjectStageService.class);

    @Resource
    private ProjectStageMapper projectStageMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageChangeService projectStageChangeService;
    @Resource
    private ProjectConvertMapper projectConvertMapper;
    @Resource
    private MsgService msgService;

    public int insert(ProjectStage record) {
        if (StringUtil.isBlank(record.getStageSt())) {
            record.setStageSt(ProjectEnum.StageStEnum.NON_START.getState());
        }
        return projectStageMapper.insert(record);
    }

    public List<ProjectStage> selectByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        return projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
    }

    public ProjectStage selectFirstStageByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        return projectStageMapper.selectFirstStageByProjectNoAndMchntCd(projectNo, mchntCd);
    }

    public void edit(String mchntCd, String projectNo, BigDecimal projectAmt, List<ProjectStageReq> stageList,
            String remark)
    {
        // 1. 获取项目信息并验证
        Project project = projectService.queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
        stageList.forEach(stage -> {
            stage.setMchntCd(mchntCd);
            stage.setProjectNo(projectNo);
        });

        // 2. 验证阶段数据
        validateStageData(stageList, projectAmt);

        // 3. 获取现有阶段信息并验证锁定状态
        List<ProjectStage> existingStages = getAndValidateExistingStages(projectNo, mchntCd);

        // 4. 转换阶段数据为实体对象
        List<ProjectStage> projectStages = projectConvertMapper.projectStageReqToProjectStageList(stageList);

        // todo 先尝试加锁，成功继续，不成功报错。如果加锁成功，那就解锁，否则不解锁。

        // 5. 处理需要删除的阶段
        processDeleteStages(projectNo, mchntCd, stageList, existingStages);

        // 6. 处理新增和更新的阶段
        processAddAndUpdateStages(projectNo, mchntCd, stageList, existingStages);

        validProjectAmt(mchntCd, projectNo, projectAmt);

        // 7. 更新项目总金额并验证
        projectService.updateProjectAmt(project, ProjectEnum.StageStEnum.ONGOING.getState(), projectAmt);

        // 8. 记录变更日志
        insertStageChangeRecord(projectNo, mchntCd, projectStages, remark);
    }

    /**
     * 验证阶段数据
     */
    private void validateStageData(List<ProjectStageReq> stageList, BigDecimal projectAmt) {
        // 验证阶段金额总和是否等于项目总金额
        validateStageAmounts(stageList, projectAmt);

        // 验证新阶段的必填字段
        validateRequiredFields(stageList);

        // 验证阶段顺序是否从1开始递增且不重复
        List<ProjectStage> projectStages = projectConvertMapper.projectStageReqToProjectStageList(stageList);
        validateStageOrder(projectStages);
    }

    /**
     * 获取并验证现有阶段信息
     */
    private List<ProjectStage> getAndValidateExistingStages(String projectNo, String mchntCd) {
        List<ProjectStage> existingStages = projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        validateStageLock(existingStages);
        return existingStages;
    }

    /**
     * 处理需要删除的阶段
     */
    private void processDeleteStages(String projectNo, String mchntCd, List<ProjectStageReq> stageList,
            List<ProjectStage> existingStages)
    {
        // 已存在的阶段信息
        Map<String, ProjectStage> existStages = existingStages.stream().collect(
                Collectors.toMap(ProjectStage::getStageNo, stage -> stage));
        Set<String> existStageNos = existStages.keySet();

        // 提交的需要更新的阶段编号
        List<String> updateStageNos = stageList.stream().filter(s -> StringUtil.isNotBlank(s.getStageNo())).map(
                ProjectStageReq::getStageNo).collect(Collectors.toList());

        // 计算需要删除的阶段编号
        existStageNos.removeAll(updateStageNos);

        // 判断是否可删除
        existStageNos.forEach(e -> {
            FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED, canDeleteStage(existStages.get(e)));
        });

        // 更新删除状态
        if (!existStageNos.isEmpty()) {
            int count = projectStageMapper.deleteByStageNo(projectNo, mchntCd, existStageNos);
            FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_DELETE_FAILURE, count == existStageNos.size());
        }
    }

    /**
     * 处理新增和更新的阶段
     */
    private void processAddAndUpdateStages(String projectNo, String mchntCd, List<ProjectStageReq> stageList,
            List<ProjectStage> existingStages)
    {
        // 将现有阶段转换为以stageNo为键的Map，便于查找
        Map<String, ProjectStage> existingStageMap = existingStages.stream().collect(
                Collectors.toMap(ProjectStage::getStageNo, stage -> stage));

        stageList.forEach(stageReq -> {
            ProjectStage projectStage = projectConvertMapper.projectStageReqToProjectStage(stageReq);
            if (StringUtil.isNotBlank(projectStage.getStageNo())) {
                // 更新阶段 - 只有当金额、排序、名称发生变化时才调用更新接口
                ProjectStage existingStage = existingStageMap.get(projectStage.getStageNo());
                if (existingStage != null && isStageChanged(projectStage, existingStage)) {
                    int count = projectStageMapper.updateByStageNo(projectStage);
                    FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_UPDATE_FAILURE, count == 1);
                }
            }
        });

        stageList.forEach(stageReq -> {
            ProjectStage projectStage = projectConvertMapper.projectStageReqToProjectStage(stageReq);
            if (StringUtil.isBlank(projectStage.getStageNo())) {
                // 新增阶段
                addNewStage(projectStage, projectNo, mchntCd);
            }
        });
    }

    /**
     * 判断阶段是否发生变化（金额、排序、名称任一变化）
     */
    private boolean isStageChanged(ProjectStage newStage, ProjectStage existingStage) {
        // 比较金额
        boolean amountChanged = !Objects.equals(newStage.getStageAmt(), existingStage.getStageAmt());
        // 比较排序
        boolean orderChanged = !Objects.equals(newStage.getStageOrder(), existingStage.getStageOrder());
        // 比较名称
        boolean nameChanged = !Objects.equals(newStage.getStageName(), existingStage.getStageName());
        return amountChanged || orderChanged || nameChanged;
    }

    /**
     * 新增阶段
     */
    private void addNewStage(ProjectStage projectStage, String projectNo, String mchntCd) {
        projectStage.setProjectNo(projectNo);
        projectStage.setMchntCd(mchntCd);
        projectStage.setStageNo(generateStageNo());
        insert(projectStage);
    }


    public void validProjectAmt(String mchntCd, String projectNo, BigDecimal projectAmt) {
        List<ProjectStage> projectStages = projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        // 校验更新后的金额是否跟项目总金额一致，不一致就报错
        BigDecimal totalStageAmt = projectStages.stream().map(ProjectStage::getStageAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_AMOUNTS_MUST_EQUAL_TOTAL_PROJECT_AMOUNTS,
                totalStageAmt.compareTo(projectAmt) == 0);
    }

    private void validateStageLock(List<ProjectStage> existingStages) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NOT_EXIST, existingStages);
        existingStages.stream().filter(s -> s.getLockFlag().equals(LockFlagEnum.LOCK.getState())).findAny().ifPresent(
                s -> {
                    FUApiAssert.failure(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED);
                });
    }

    private void insertStageChangeRecord(String projectNo, String mchntCd, List<ProjectStage> stageList, String remark)
    {
        // 获取更新后的阶段列表
        List<ProjectStage> existingStages = projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        // 存在，按照编号处理
        Map<String, ProjectStage> existingMap = Optional.ofNullable(existingStages).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(ProjectStage::getStageNo, s -> s));
        // 提交，有编号的阶段列表
        Map<String, ProjectStage> updateStageMaps = Optional.ofNullable(stageList).orElse(Collections.emptyList())
                .stream().filter(s -> StringUtil.isNotBlank(s.getStageNo())).collect(
                        Collectors.toMap(ProjectStage::getStageNo, s -> s));
        // 提交，没编号的，新增数据
        List<ProjectStage> addStageLists = Optional.ofNullable(stageList).orElse(Collections.emptyList()).stream()
                .filter(s -> StringUtil.isBlank(s.getStageNo())).collect(Collectors.toList());
        // 收集变更信息
        List<String> changes = new ArrayList<>();
        addStageLists.forEach(stage -> {
            changes.add(String.format("新增阶段%s: 名称=%s, 金额=%s", stage.getStageOrder(), stage.getStageName(),
                    stage.getStageAmt()));
        });
        List<Map<String, Object>> changedMapList = new ArrayList<>();
        // 比较修改的阶段
        existingMap.forEach((order, oldStage) -> {
            // 删除
            if (!updateStageMaps.keySet().contains(order)) {
                changes.add(String.format("删除阶段%s: 编号=%s, 名称=%s, 金额=%s", oldStage.getStageOrder(),
                        oldStage.getStageNo(), oldStage.getStageName(), oldStage.getStageAmt()));
                return;
            }
            // 更新
            ProjectStage newStage = updateStageMaps.get(order);
            if (!Objects.equals(oldStage.getStageName(), newStage.getStageName())) {
                changes.add(String.format("阶段编号%s名称变更: %s -> %s", order, oldStage.getStageName(),
                        newStage.getStageName()));
            }
            if (oldStage.getStageAmt() != null && newStage.getStageAmt() != null && oldStage.getStageAmt().compareTo(
                    newStage.getStageAmt()) != 0)
            {
                changes.add(String.format("阶段编号%s金额变更: %s -> %s", order, oldStage.getStageAmt(),
                        newStage.getStageAmt()));
                HashMap<String, Object> notifyMap = MapUtil.newHashMap();
                notifyMap.put("oldState", oldStage);
                notifyMap.put("newState", newStage);
                changedMapList.add(notifyMap);
            }
            if (!Objects.equals(oldStage.getStageOrder(), newStage.getStageOrder())) {
                changes.add(String.format("阶段编号%s顺序变更: %s -> %s", order, oldStage.getStageOrder(),
                        newStage.getStageOrder()));
            }
        });
        // 插入变更记录
        String content = changes.isEmpty() ? "无变更" : String.join("\n", changes);
        projectStageChangeService.insert(projectNo, mchntCd, content + "\n\n备注内容：" + remark);
        if (CollUtil.isNotEmpty(changedMapList)) {
            changedMapList.forEach(change -> {
                msgService.addProjectAmtChangeMsg((ProjectStage) change.get("oldState"),
                        (ProjectStage) change.get("newState"), remark);
            });
        }
    }


    /**
     * 检查阶段是否可以更新
     */
    private boolean canUpdateStage(ProjectStage stage) {
        // 已完成的阶段不能更新
        if (ProjectEnum.StageStEnum.COMPLETED.getState().equals(stage.getStageSt())) {
            return false;
        }

        // 已锁定的阶段不能更新
        if (LockFlagEnum.LOCK.getState().equals(stage.getLockFlag())) {
            return false;
        }

        // 进行中的阶段需要检查是否有收款
        if (ProjectEnum.StageStEnum.ONGOING.getState().equals(stage.getStageSt())) {
            return stage.getStageActualAmt() == null || stage.getStageActualAmt().compareTo(BigDecimal.ZERO) <= 0;
        }

        // 未开始的阶段可以更新
        return true;
    }

    /**
     * 检查阶段是否可以删除
     */
    private boolean canDeleteStage(ProjectStage stage) {
        if (!ProjectEnum.StageStEnum.NON_START.getState().equals(stage.getStageSt())) {
            return false;
        }
        return canUpdateStage(stage);
    }

    /**
     * 验证阶段金额总和是否等于项目总金额
     */
    private void validateStageAmounts(List<ProjectStageReq> stageList, BigDecimal projectAmt) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, stageList);
        BigDecimal totalStageAmt = stageList.stream().map(ProjectStageReq::getStageAmt).filter(Objects::nonNull).reduce(
                BigDecimal.ZERO, BigDecimal::add);
        FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_AMOUNTS_MUST_EQUAL_TOTAL_PROJECT_AMOUNTS,
                totalStageAmt.compareTo(projectAmt) == 0);
    }

    /**
     * 添加的阶段金额为必填，否则提示"请完成页面设置"
     */
    private void validateRequiredFields(List<ProjectStageReq> stageList) {
        for (ProjectStageReq stage : stageList) {
            FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY,
                    stage.getStageOrder() == null || !StringUtils.hasText(stage.getStageName()));
            FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY,
                    stage.getStageAmt() == null || stage.getStageAmt().compareTo(BigDecimal.ZERO) <= 0);
        }
    }

    /**
     * 验证阶段顺序是否从1开始递增且不重复
     */
    private void validateStageOrder(List<ProjectStage> stageList) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, stageList);
        // 按照阶段顺序排序
        List<Integer> orders = stageList.stream().sorted(
                Comparator.comparing(ProjectStage::getStageOrder, Comparator.nullsLast(Comparator.naturalOrder()))).map(
                s -> s.getStageOrder()).collect(Collectors.toList());
        FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_ORDER_MUST_START_FROM_ONE_AND_INCREASE,
                isValidSequence(orders));
    }

    private boolean isValidSequence(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        return IntStream.range(0, list.size()).allMatch(i -> list.get(i) == i + 1);
    }

    private String generateStageNo() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    public void batchSaveStages(String projectNo, String mchntCd, List<ProjectStage> stages) {
        // 验证阶段顺序是否从1开始递增且不重复
        validateStageOrder(stages);

        // 按阶段顺序排序
        List<ProjectStage> sortedStages = stages.stream().sorted(
                        Comparator.comparing(ProjectStage::getStageOrder,
                                Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        // 保存所有阶段
        IntStream.range(0, sortedStages.size()).forEach(i -> {
            ProjectStage stage = sortedStages.get(i);
            stage.setProjectNo(projectNo);
            stage.setMchntCd(mchntCd);
            // 生成阶段编号
            stage.setStageNo(generateStageNo());
            // 第一个阶段进行中，其他未开始
            stage.setStageSt(
                    i == 0 ? ProjectEnum.StageStEnum.ONGOING.getState() : ProjectEnum.StageStEnum.NON_START.getState());
            insert(stage);
        });
    }

    public int editAmt(ProjectStageReq projectStageReq) throws Exception {
        ProjectStage stage = queryProjectStage(projectStageReq.getProjectNo(), projectStageReq.getMchntCd(),
                projectStageReq.getStageNo());
        // 验证
        vaildAmtAndLock(stage);
        // 验证参数必填
        validParamsNonNull(projectStageReq);
        // 计算金额差值
        BigDecimal diffAmt = projectStageReq.getStageAmt().subtract(stage.getStageAmt());
        // 更新项目总金额
        Project project = projectService.queryProjectByProjectNoAndMchntCd(projectStageReq.getProjectNo(),
                projectStageReq.getMchntCd());

        BigDecimal newProjectAmt = project.getProjectAmt().add(diffAmt);

        // 更新阶段金额
        ProjectStage updateStage = new ProjectStage();
        updateStage.setRowId(stage.getRowId());
        updateStage.setStageAmt(projectStageReq.getStageAmt());
        updateStage.setReserved1(projectStageReq.getReserved1());
        int result = 0;
        try {
            projectService.updateProjectAmt(projectStageReq.getProjectNo(), stage.getMchntCd(), newProjectAmt);
            result = projectStageMapper.updateByPrimaryKey(updateStage);
        } catch (Exception e) {
            // 执行失败，补偿操作
            updateStage.setStageAmt(stage.getStageAmt());
            updateStage.setReserved1(stage.getReserved1());
            compensateEditAmtOperation(projectStageReq.getProjectNo(), projectStageReq.getMchntCd(),
                    project.getProjectAmt(), updateStage);
            throw e;
        }
        // 插入变更记录
        String typeTxt = diffAmt.compareTo(BigDecimal.ZERO) > 0 ? "增加" : "减少";
        String content = String.format("项目总金额: %s -> %s，阶段%s金额: %s -> %s，本期%s金额：%s ，备注: %s ",
                project.getProjectAmt(), newProjectAmt, stage.getStageOrder(), stage.getStageAmt(),
                projectStageReq.getStageAmt(), typeTxt, diffAmt, projectStageReq.getReserved1());
        projectStageChangeService.insert(stage.getProjectNo(), stage.getMchntCd(), content);
        return result;
    }

    private void compensateEditAmtOperation(String projectNo, String mchntCd, BigDecimal oldProjectAmt,
            ProjectStage updateStage)
    {
        LogWriter.info(String.format("开始执行增减金额失败补偿操作，项目编号：%s，商户号：%s", projectNo, mchntCd));
        List<CompensationStep> steps = Arrays.asList(new CompensationStep("项目数据更新回滚",
                        () -> projectService.updateProjectAmt(projectNo, mchntCd, oldProjectAmt),
                        String.format("projectNo=%s,mchntCd=%s,oldProjectAmt=%s", projectNo, mchntCd, oldProjectAmt)),
                new CompensationStep("项目阶段数据更新回滚", () -> projectStageMapper.updateByPrimaryKey(updateStage),
                        String.format("rowId=%s,stageAmt=%s,reserved1=%s", updateStage.getRowId(),
                                updateStage.getStageAmt(), updateStage.getReserved1())));
        projectService.runCompensate(steps);
    }

    private void vaildAmtAndLock(ProjectStage stage) {
        FUApiAssert.equals(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED, stage.getStageSt(),
                ProjectEnum.StageStEnum.COMPLETED.getState());
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_PAYED_AND_AMT_NON_MODIFIED,
                stage.getStageActualAmt() != null && stage.getStageActualAmt().compareTo(BigDecimal.ZERO) > 0);
        FUApiAssert.equals(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED, stage.getLockFlag(),
                LockFlagEnum.UNLOCK.getState());
    }

    @LogAnnotation("项目阶段-查询阶段详情")
    public ProjectStage queryProjectStage(String projectNo, String mchntCd, String stageNo) {
        return projectStageMapper.selectStageByProjectNoAndMchntCdAndNo(projectNo, mchntCd, stageNo);
    }

    /**
     * 验证阶段编辑参数非空
     *
     * @param projectStageReq 阶段请求参数
     */
    private void validParamsNonNull(ProjectStageReq projectStageReq) {
        FUApiAssert.notNull(ResponseCodeEnum.PARAM_ERROR, projectStageReq);
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "项目编号不能为空",
                projectStageReq.getProjectNo());
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "商户号不能为空",
                projectStageReq.getMchntCd());
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "阶段编号不能为空",
                projectStageReq.getStageNo());
        FUApiAssert.isFalse(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "阶段金额不能为空，且为正整数",
                projectStageReq.getStageAmt() == null || projectStageReq.getStageAmt().compareTo(BigDecimal.ZERO) <= 0);
    }

    public void deleteByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        // 根据项目编号删除项目阶段数据
        projectStageMapper.deleteByProjectNoAndMchntCd(projectNo, mchntCd);
    }


    public static void validProjectStage(ProjectStage projectStage) {
        if (projectStage == null) {
            throw new FUException(ResponseCodeEnum.PROJECT_NON_EXIST);
        }
        if (!ProjectEnum.ProjectStEnum.ONGOING.getState().equals(projectStage.getStageSt())) {
            log.info("项目阶段状态为：{}，项目锁定状态为：{}，不允许执行此操作", projectStage.getStageSt(),
                    projectStage.getLockFlag());
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
        if (!LockFlagEnum.UNLOCK.getState().equals(projectStage.getLockFlag())) {
            log.info("项目阶段状态为：{}，项目锁定状态为：{}，不允许执行此操作", projectStage.getStageSt(),
                    projectStage.getLockFlag());
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "查询项目阶段列表")
    public List<ProjectStage> queryProjectStages(String mchntCd, List<String> projectNos, List<String> stageSts) {
        return projectStageMapper.selectByProjectNos(mchntCd, projectNos, stageSts);
    }


    /***
     *汇总项目实收金额
     * @param mchntCd :
     * @param projectNos :
     * @param stageSts :
     * @return: java.math.BigDecimal
     * @Author: Joker
     * @Date: 2025/5/21 21:50
     */


    public BigDecimal sumProjectPaidInAmt(String mchntCd, List<String> projectNos, List<String> stageSts) {

        List<ProjectStage> projectStages = ApplicationContextKeeper.getBean(this.getClass()).queryProjectStages(mchntCd,
                projectNos, stageSts);
        if (CollectionUtils.isEmpty(projectStages)) {
            return BigDecimal.ZERO;
        }
        BigDecimal bigDecimal = BigDecimal.ZERO;
        projectStages.parallelStream().filter(Objects::nonNull).filter(
                projectStage -> projectStage.getStageActualAmt() != null).forEach(projectStage -> {
            bigDecimal.add(sumStagePaidInAmt(projectStage));
        });

        return bigDecimal;
    }

    /**
     * 汇总阶段实收金额
     *
     * @param projectStage :
     * @return: java.math.BigDecimal
     * @Author: Joker
     * @Date: 2025/5/21 21:49
     */

    public static BigDecimal sumStagePaidInAmt(ProjectStage projectStage) {

        if (projectStage == null) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal("0").add(
                projectStage.getStageActualAmt() == null ? BigDecimal.ZERO : projectStage.getStageActualAmt()).subtract(
                projectStage.getRefundAmt() == null ? BigDecimal.ZERO : projectStage.getRefundAmt());
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段锁定状态")
    public int updateLock(String mchntCd, String projectNo, String stageNo) {
        return projectStageMapper.updateLock(mchntCd, projectNo, stageNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段锁定状态")
    public int updateUnLock(String mchntCd, String projectNo, String stageNo) {
        return projectStageMapper.updateUnLock(mchntCd, projectNo, stageNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "查询项目阶段list")
    public List<ProjectStage> selectByMchntCdAndProjectNo(String mchntCd, String projectNo) {
        return projectStageMapper.selectByMchntCdAndProjectNo(mchntCd, projectNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段")
    public int updateStageForTxnWithoutUnLock(ProjectStage projectStageThisTxn) {
        return projectStageMapper.updateStageForTxnWithoutUnLock(projectStageThisTxn);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "开启下一阶段状态")
    public int startStageForTxn(String mchntCd, String projectNo, String stageNo) {
        return projectStageMapper.startStageForTxn(mchntCd, projectNo, stageNo);
    }
}
